# Gemini-Powered Image Variation Combiner

A comprehensive Python toolkit that combines multiple image variations into single composite images using **Google's Gemini AI** for advanced image understanding and similarity detection.

## 🆕 NEW: Gemini AI Integration

This version features **Google Gemini AI-powered image similarity detection**, providing:
- **🤖 Gemini Integration**: Advanced semantic understanding of image content for highly accurate similarity detection
- **🔍 Dual Detection Methods**: Choose between Gemini AI (semantic) or imagehash (fast pixel-based)
- **⚡ Automatic Fallback**: Seamlessly falls back to imagehash if Gemini is unavailable
- **🎯 Adjustable Thresholds**: Fine-tune similarity sensitivity for your specific needs
- **📊 Real-time Processing**: Monitor progress with detailed logging

## Features

- **AI-Powered Similarity**: Uses Gemini 2.5 Flash for semantic image understanding
- **Multiple Layout Options**: Grid, horizontal, and vertical arrangements
- **Automatic Resizing**: Uniform sizing with aspect ratio preservation
- **Customizable Spacing**: Adjustable gaps between images
- **Background Colors**: Configurable background colors
- **High Quality Output**: Maintains image quality during processing
- **User-Friendly GUI**: Easy-to-use graphical interface

## Installation

### Quick Setup

1. **Clone or download this repository**

2. **Install dependencies:**
   ```bash
   pip install -r gemini_requirements.txt
   ```

3. **Get your Gemini API key:**
   - Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Create a new API key
   - Copy the API key for use in the application

### System Requirements
- **Python**: 3.7 or higher
- **Memory**: 4GB+ RAM (8GB+ recommended)
- **Internet**: Required for Gemini API calls
- **API Key**: Google Gemini API key

## Usage

### 🖥️ GUI Application (Recommended)

```bash
python launch_gemini_gui.py
```

**GUI Features:**
- **Gemini API Configuration**: Enter your API key and select model
- **Similarity Method Selection**: Toggle between Gemini AI and imagehash
- **Gemini Threshold Control**: Adjust similarity sensitivity (0.5=loose, 0.95=strict)
- **Real-time Processing Log**: Monitor progress and see detailed information
- **Visual Settings**: Layout, spacing, quality, and background options

### 🤖 Command Line Interface

```bash
# Use Gemini for high-accuracy similarity detection
python gemini_variation_finder.py /path/to/image/folder --api-key YOUR_API_KEY

# Specify output directory and Gemini threshold
python gemini_variation_finder.py /path/to/images -o output_batches --gemini-threshold 0.8 --api-key YOUR_API_KEY

# Custom batch size with Gemini
python gemini_variation_finder.py /path/to/images --batch-size 6 --gemini-threshold 0.85 --api-key YOUR_API_KEY

# Use traditional imagehash method (faster, no API required)
python gemini_variation_finder.py /path/to/images --no-gemini --hash-threshold 10

# Fine-tune similarity detection
python gemini_variation_finder.py /path/to/images --gemini-threshold 0.9 --api-key YOUR_API_KEY  # Very strict
python gemini_variation_finder.py /path/to/images --gemini-threshold 0.7 --api-key YOUR_API_KEY  # More loose
```

## How Gemini Detection Works

1. **Scans Directory**: Recursively finds all image files
2. **AI Analysis**: Uses Gemini AI to understand image content and context
3. **Semantic Comparison**: Compares images based on content, not just pixels
4. **Groups Similar Images**: Creates groups based on AI-determined similarity
5. **Creates Batches**: Groups similar images into batches of specified size
6. **Combines Images**: Automatically creates composite images for each batch
7. **Generates Reports**: Creates text files listing source images for each batch

## Gemini Models Supported

- **gemini-2.5-flash** (Default) - Latest and fastest model
- **gemini-2.0-flash-exp** - Experimental version
- **gemini-1.5-flash** - Previous generation, fast
- **gemini-1.5-pro** - More capable but slower

## API Key Setup

### Option 1: Environment Variable (Recommended)
```bash
export GEMINI_API_KEY="your_api_key_here"
```

### Option 2: GUI Entry
Enter your API key directly in the GUI interface

### Option 3: Command Line
```bash
python gemini_variation_finder.py /path/to/images --api-key YOUR_API_KEY
```

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)
- GIF (.gif)
- WebP (.webp)

## Examples

### Creating AI-Powered Photo Groups

The Gemini AI can understand:
- **Same person** in different poses or lighting
- **Same object** from different angles
- **Similar scenes** or compositions
- **Style variations** of the same subject

### Before/After Comparison

Gemini can identify:
- **Edited vs original** photos
- **Different processing** of the same image
- **Seasonal changes** of the same location
- **Time progression** photos

## Tips

1. **API Usage**: Gemini API calls cost money - use hash fallback for large batches
2. **Threshold Tuning**: Start with 0.8, adjust based on your specific needs
3. **Batch Size**: Smaller batches (2-4) work better for precise grouping
4. **Internet Required**: Gemini requires internet connection for API calls
5. **Rate Limits**: Be aware of API rate limits for large image collections

## Error Handling

The tool includes robust error handling:
- Automatic fallback to hash-based detection if Gemini fails
- Skips corrupted or unreadable image files
- Provides informative error messages
- Continues processing valid images even if some fail

## Cost Considerations

- Gemini API has usage costs based on the number of requests
- Each image comparison requires an API call
- Consider using hash-based detection for large collections
- Monitor your API usage in Google Cloud Console

## License

This project is open source and available under the MIT License.

## Support

For issues related to:
- **Gemini API**: Check [Google AI documentation](https://ai.google.dev/)
- **Application bugs**: Create an issue in this repository
- **Feature requests**: Submit a feature request
