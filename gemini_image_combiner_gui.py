#!/usr/bin/env python3
"""
Gemini-Powered Image Variation Combiner GUI

A user-friendly interface for combining image variations using Google's Gemini API
for advanced image understanding and similarity detection.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
import math
from pathlib import Path
from typing import Op<PERSON>, List, Tuple
from gemini_variation_finder import GeminiImageVariationFinder
from image_combiner import ImageCombiner

class GeminiImageCombinerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Gemini-Powered Image Variation Combiner")
        self.root.geometry("900x800")
        self.root.resizable(True, True)

        # Set minimum window size
        self.root.minsize(850, 750)
        
        # Variables
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.layout_mode = tk.StringVar(value="grid")
        self.batch_size = tk.IntVar(value=4)
        self.output_quality = tk.IntVar(value=95)
        self.spacing = tk.IntVar(value=10)
        self.background_color = tk.StringVar(value="white")
        
        # Gemini variables
        self.gemini_threshold = tk.DoubleVar(value=0.8)
        self.gemini_model = tk.StringVar(value="gemini-2.5-flash")
        self.api_key = tk.StringVar(value="AIzaSyAPOtiX70Sm_sHKyJ6Rrgl2tbJ_NswfR1A")
        
        # Hash variables
        self.hash_threshold = tk.IntVar(value=10)
        self.use_gemini = tk.BooleanVar(value=True)
        self.use_hash = tk.BooleanVar(value=True)
        
        # Additional options
        self.generate_master_file = tk.BooleanVar(value=False)
        
        # Progress tracking
        self.is_processing = False
        
        self.create_widgets()
        
    def create_widgets(self):
        # Create a canvas and scrollbar for scrollable content
        canvas = tk.Canvas(self.root)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Main container with padding
        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure grid weights
        scrollable_frame.columnconfigure(0, weight=1)
        scrollable_frame.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Bind mousewheel to canvas for scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # Title
        title_label = ttk.Label(main_frame, text="Gemini-Powered Image Variation Combiner", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input Directory Section
        ttk.Label(main_frame, text="Input Directory:", font=('Arial', 10, 'bold')).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, columnspan=3, sticky="we", pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(input_frame, textvariable=self.input_dir, width=60).grid(
            row=0, column=0, sticky="we", padx=(0, 10))
        ttk.Button(input_frame, text="Browse", 
                  command=self.browse_input_dir).grid(row=0, column=1)
        
        # Output Directory Section
        ttk.Label(main_frame, text="Output Directory:", font=('Arial', 10, 'bold')).grid(
            row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=4, column=0, columnspan=3, sticky="we", pady=(0, 15))
        output_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(output_frame, textvariable=self.output_dir, width=60).grid(
            row=0, column=0, sticky="we", padx=(0, 10))
        ttk.Button(output_frame, text="Browse", 
                  command=self.browse_output_dir).grid(row=0, column=1)
        
        # Gemini API Configuration Section
        gemini_frame = ttk.LabelFrame(main_frame, text="Gemini API Configuration", padding="15")
        gemini_frame.grid(row=5, column=0, columnspan=3, sticky="we", pady=(0, 15))
        gemini_frame.columnconfigure(1, weight=1)
        
        # API Key
        ttk.Label(gemini_frame, text="API Key:").grid(row=0, column=0, sticky=tk.W, pady=5)
        api_key_entry = ttk.Entry(gemini_frame, textvariable=self.api_key, width=50, show="*")
        api_key_entry.grid(row=0, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Model Selection
        ttk.Label(gemini_frame, text="Gemini Model:").grid(row=1, column=0, sticky=tk.W, pady=5)
        model_combo = ttk.Combobox(gemini_frame, textvariable=self.gemini_model, 
                                  values=["gemini-2.5-flash", "gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro"], 
                                  state="readonly")
        model_combo.grid(row=1, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Gemini Threshold
        ttk.Label(gemini_frame, text="Gemini Similarity Threshold:").grid(row=2, column=0, sticky=tk.W, pady=5)
        threshold_frame = ttk.Frame(gemini_frame)
        threshold_frame.grid(row=2, column=1, sticky="we", padx=(10, 0), pady=5)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.5, to=0.95, variable=self.gemini_threshold, 
                                   orient=tk.HORIZONTAL)
        threshold_scale.grid(row=0, column=0, sticky="we")
        threshold_frame.columnconfigure(0, weight=1)
        self.gemini_threshold_label = ttk.Label(threshold_frame, text="0.80")
        self.gemini_threshold_label.grid(row=0, column=1, padx=(10, 0))
        
        # Update threshold label when scale changes
        self.gemini_threshold.trace_add('write', self.update_gemini_threshold_label)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Processing Settings", padding="15")
        settings_frame.grid(row=6, column=0, columnspan=3, sticky="we", pady=(0, 15))
        settings_frame.columnconfigure(1, weight=1)
        
        # Layout Mode
        ttk.Label(settings_frame, text="Layout Mode:").grid(row=0, column=0, sticky=tk.W, pady=5)
        layout_combo = ttk.Combobox(settings_frame, textvariable=self.layout_mode, 
                                   values=["grid", "horizontal", "vertical"], state="readonly")
        layout_combo.grid(row=0, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Max Batch Size
        ttk.Label(settings_frame, text="Max Batch Size:").grid(row=1, column=0, sticky=tk.W, pady=5)
        batch_frame = ttk.Frame(settings_frame)
        batch_frame.grid(row=1, column=1, sticky="we", padx=(10, 0), pady=5)
        batch_spin = ttk.Spinbox(batch_frame, from_=2, to=16, textvariable=self.batch_size, width=10)
        batch_spin.grid(row=0, column=0, sticky=tk.W)
        ttk.Label(batch_frame, text="(similarity groups batches)", foreground="gray", font=('Arial', 8)).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Spacing
        ttk.Label(settings_frame, text="Image Spacing (px):").grid(row=2, column=0, sticky=tk.W, pady=5)
        spacing_spin = ttk.Spinbox(settings_frame, from_=0, to=100, textvariable=self.spacing, width=10)
        spacing_spin.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Background Color
        ttk.Label(settings_frame, text="Background Color:").grid(row=3, column=0, sticky=tk.W, pady=5)
        color_combo = ttk.Combobox(settings_frame, textvariable=self.background_color,
                                  values=["white", "black", "gray", "transparent"], state="readonly")
        color_combo.grid(row=3, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Quality
        ttk.Label(settings_frame, text="Output Quality (%):").grid(row=4, column=0, sticky=tk.W, pady=5)
        quality_spin = ttk.Spinbox(settings_frame, from_=70, to=100, textvariable=self.output_quality, width=10)
        quality_spin.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Generate Master File
        ttk.Label(settings_frame, text="Generate Master File:").grid(row=5, column=0, sticky=tk.W, pady=5)
        master_check = ttk.Checkbutton(settings_frame, variable=self.generate_master_file)
        master_check.grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Similarity Detection Section
        similarity_frame = ttk.LabelFrame(main_frame, text="Similarity Detection Methods", padding="15")
        similarity_frame.grid(row=7, column=0, columnspan=3, sticky="we", pady=(0, 15))
        similarity_frame.columnconfigure(1, weight=1)

        # Method selection checkboxes
        ttk.Label(similarity_frame, text="Detection Methods:", font=('Arial', 10, 'bold')).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Gemini method
        gemini_method_frame = ttk.Frame(similarity_frame)
        gemini_method_frame.grid(row=1, column=0, columnspan=2, sticky="we", pady=2)
        ttk.Checkbutton(gemini_method_frame, text="Gemini AI (Semantic Similarity)", variable=self.use_gemini).grid(row=0, column=0, sticky=tk.W)
        ttk.Label(gemini_method_frame, text="- Advanced AI-based content understanding", foreground="gray", font=('Arial', 8)).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        # Perceptual Hash method
        hash_frame = ttk.Frame(similarity_frame)
        hash_frame.grid(row=2, column=0, columnspan=2, sticky="we", pady=2)
        ttk.Checkbutton(hash_frame, text="Perceptual Hashing", variable=self.use_hash).grid(row=0, column=0, sticky=tk.W)
        ttk.Label(hash_frame, text="- Fast structural similarity detection", foreground="gray", font=('Arial', 8)).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        # Hash threshold
        ttk.Label(similarity_frame, text="Hash Threshold:").grid(row=3, column=0, sticky=tk.W, pady=5)
        hash_threshold_frame = ttk.Frame(similarity_frame)
        hash_threshold_frame.grid(row=3, column=1, sticky="we", padx=(10, 0), pady=5)
        hash_threshold_scale = ttk.Scale(hash_threshold_frame, from_=5, to=20, variable=self.hash_threshold, orient=tk.HORIZONTAL)
        hash_threshold_scale.grid(row=0, column=0, sticky="we")
        hash_threshold_frame.columnconfigure(0, weight=1)
        self.hash_threshold_label = ttk.Label(hash_threshold_frame, text="10")
        self.hash_threshold_label.grid(row=0, column=1, padx=(10, 0))

        # Update threshold labels when scales change
        self.hash_threshold.trace_add('write', self.update_hash_threshold_label)

        # Progress Section
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=8, column=0, columnspan=3, sticky="we", pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.StringVar(value="Ready to process images...")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky="we", pady=(5, 0))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=9, column=0, columnspan=3, pady=(15, 10), sticky="ew")

        # Center the buttons
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)

        self.process_button = ttk.Button(button_container, text="Process Images",
                                        command=self.start_processing, style='Accent.TButton')
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_container, text="Open Output Folder",
                  command=self.open_output_folder).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_container, text="Reset Settings",
                  command=self.reset_settings).pack(side=tk.LEFT)

        # Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Processing Log", padding="10")
        log_frame.grid(row=10, column=0, columnspan=3, sticky="nsew", pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # Text widget with scrollbar
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=0, column=0, sticky="nsew")
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        self.log_text = tk.Text(text_frame, height=6, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky="nsew")
        log_scrollbar.grid(row=0, column=1, sticky="ns")

        # Configure main frame row weights
        main_frame.rowconfigure(10, weight=1)

    def browse_input_dir(self):
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)
            self.log(f"Input directory selected: {directory}")

    def browse_output_dir(self):
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)
            self.log(f"Output directory selected: {directory}")

    def update_gemini_threshold_label(self, *args):
        """Update the Gemini threshold label"""
        self.gemini_threshold_label.config(text=f"{self.gemini_threshold.get():.2f}")

    def update_hash_threshold_label(self, *args):
        """Update the hash threshold label"""
        self.hash_threshold_label.config(text=f"{self.hash_threshold.get()}")

    def reset_settings(self):
        """Reset all settings to default values"""
        self.layout_mode.set("grid")
        self.batch_size.set(4)
        self.output_quality.set(95)
        self.spacing.set(10)
        self.background_color.set("white")
        self.gemini_threshold.set(0.8)
        self.gemini_model.set("gemini-2.5-flash")
        self.hash_threshold.set(10)
        self.use_gemini.set(True)
        self.use_hash.set(True)
        self.generate_master_file.set(False)

        self.log("Settings reset to defaults")

    def open_output_folder(self):
        if self.output_dir.get() and os.path.exists(self.output_dir.get()):
            os.system(f'open "{self.output_dir.get()}"')
        else:
            messagebox.showwarning("Warning", "Output directory not found or not set")

    def log(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def start_processing(self):
        if self.is_processing:
            return

        # Validate inputs
        if not self.input_dir.get():
            messagebox.showerror("Error", "Please select an input directory")
            return

        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory")
            return

        if not os.path.exists(self.input_dir.get()):
            messagebox.showerror("Error", "Input directory does not exist")
            return

        if not self.api_key.get():
            messagebox.showerror("Error", "Please enter your Gemini API key")
            return

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)

        # Start processing in a separate thread
        self.is_processing = True
        self.process_button.config(state='disabled')
        self.progress_bar.start()

        thread = threading.Thread(target=self.process_images)
        thread.daemon = True
        thread.start()

    def process_images(self):
        try:
            self.progress_var.set("Initializing Gemini image processing...")
            self.log("=" * 50)
            self.log("Starting Gemini-powered image processing...")

            # Log settings
            self.log(f"Layout mode: {self.layout_mode.get()}")
            self.log(f"Max batch size: {self.batch_size.get()}")
            self.log(f"Spacing: {self.spacing.get()}px")
            self.log(f"Gemini model: {self.gemini_model.get()}")
            self.log(f"Gemini threshold: {self.gemini_threshold.get():.2f}")
            if self.generate_master_file.get():
                self.log("Will generate master file with all batch information")

            # Initialize the Gemini variation finder
            try:
                finder = GeminiImageVariationFinder(
                    gemini_threshold=self.gemini_threshold.get(),
                    gemini_model=self.gemini_model.get(),
                    hash_threshold=self.hash_threshold.get(),
                    use_gemini=self.use_gemini.get(),
                    use_hash=self.use_hash.get(),
                    api_key=self.api_key.get()
                )
            except Exception as e:
                error_msg = f"Failed to initialize Gemini similarity detector: {str(e)}"
                self.log(error_msg)
                raise RuntimeError(error_msg)

            # Log active similarity methods
            active_methods = []
            if self.use_gemini.get():
                active_methods.append(f"Gemini ({self.gemini_model.get()}, threshold: {self.gemini_threshold.get():.2f})")
            if self.use_hash.get():
                active_methods.append(f"Perceptual Hash (threshold: {self.hash_threshold.get()})")

            self.log(f"Active similarity methods: {', '.join(active_methods)}")

            # Process the directory
            self.progress_var.set("Scanning for similar images...")
            batches = finder.process_directory(
                input_dir=self.input_dir.get(),
                output_dir=self.output_dir.get(),
                batch_size=self.batch_size.get(),
                target_size=None,  # Use dynamic sizing based on image content
                layout_mode=self.layout_mode.get(),
                spacing=self.spacing.get(),
                background_color=self.background_color.get(),
                quality=self.output_quality.get(),
                generate_master_file=self.generate_master_file.get()
            )

            self.progress_var.set(f"Processing complete!")
            self.log(f"\nProcessing complete!")

            # Count the total number of processed images
            total_images = sum(len(batch) for batch in batches) if batches else 0

            # Additional images may have been processed as individual files
            input_dir = self.input_dir.get()
            from pathlib import Path
            img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
            all_images = []
            for ext in img_extensions:
                all_images.extend(list(Path(input_dir).glob(f"**/*{ext}")))
                all_images.extend(list(Path(input_dir).glob(f"**/*{ext.upper()}")))

            total_input_images = len(set(str(p) for p in all_images))

            # Count single image batches
            single_image_batches = sum(1 for batch in batches if batch and len(batch) == 1) if batches else 0
            multi_image_batches = len(batches) - single_image_batches if batches else 0

            self.log(f"Total input images: {total_input_images}")
            self.log(f"Generated {len(batches) if batches else 0} image batches")
            if single_image_batches > 0:
                self.log(f"- {single_image_batches} single images preserved as-is")
            if multi_image_batches > 0:
                self.log(f"- {multi_image_batches} groups of similar images combined")
            self.log(f"Output saved to: {self.output_dir.get()}")

            # Show completion message
            self.root.after(0, lambda: messagebox.showinfo(
                "Success",
                f"Gemini processing complete!\n\nProcessed {total_input_images} input images\n"
                f"Generated {len(batches) if batches else 0} batches:\n"
                f"- {single_image_batches} single images preserved as-is\n"
                f"- {multi_image_batches} similar image groups combined\n\n"
                f"Using Gemini AI for similarity detection\n\nCheck the output directory for results."
            ))

        except Exception as e:
            error_msg = f"Error during processing: {str(e)}"
            self.log(error_msg)
            self.progress_var.set("Processing failed")
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))

        finally:
            # Reset UI state
            self.is_processing = False
            self.root.after(0, lambda: [
                self.progress_bar.stop(),
                self.process_button.config(state='normal'),
                self.progress_var.set("Ready to process images...")
            ])


def main():
    root = tk.Tk()

    # Set up modern styling
    style = ttk.Style()

    # Try to use a modern theme
    available_themes = style.theme_names()
    if 'aqua' in available_themes:  # macOS
        style.theme_use('aqua')
    elif 'vista' in available_themes:  # Windows
        style.theme_use('vista')
    elif 'clam' in available_themes:  # Cross-platform modern
        style.theme_use('clam')

    app = GeminiImageCombinerGUI(root)

    # Center the window
    root.update_idletasks()
    width = 900
    height = 800
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
