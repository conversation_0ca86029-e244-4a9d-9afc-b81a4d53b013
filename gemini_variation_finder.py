#!/usr/bin/env python3
"""
Gemini-Powered Image Variation Finder and Combiner

A Python tool that automatically finds similar image variations using Google's Gemini API
for advanced image understanding and similarity detection.
"""

import os
import sys
import base64
from PIL import Image
import argparse
from typing import List, Tuple, Dict, Set, Optional
from collections import defaultdict
import glob
from image_combiner import Image<PERSON>omb<PERSON>
import shutil
import numpy as np
import hashlib
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from functools import partial
import time
import json

# Import required libraries with fallbacks
try:
    from google import genai
    from google.genai import types
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: Google Gemini not available. Install with: pip install google-genai")

try:
    import imagehash
    IMAGEHASH_AVAILABLE = True
except ImportError:
    IMAGEHASH_AVAILABLE = False
    print("Warning: imagehash not available. Install with: pip install imagehash")

# Check if we have at least one similarity method available
if not GEMINI_AVAILABLE and not IMAGEHASH_AVAILABLE:
    print("Error: No similarity detection methods available.")
    print("Please install either google-genai or imagehash")
    sys.exit(1)

class GeminiImageVariationFinder:
    """Enhanced image variation finder using Gemini API for similarity detection"""
    
    def __init__(self, 
                 gemini_threshold: float = 0.8,
                 gemini_model: str = "gemini-2.0-flash-exp",
                 hash_threshold: int = 10,
                 use_gemini: bool = True,
                 use_hash: bool = True,
                 api_key: Optional[str] = None):
        """
        Initialize the Gemini-powered variation finder
        
        Args:
            gemini_threshold: Similarity threshold for Gemini (0.0-1.0)
            gemini_model: Gemini model to use
            hash_threshold: Threshold for perceptual hash comparison
            use_gemini: Whether to use Gemini for similarity detection
            use_hash: Whether to use perceptual hashing
            api_key: Gemini API key (if not set in environment)
        """
        self.gemini_threshold = gemini_threshold
        self.gemini_model = gemini_model
        self.hash_threshold = hash_threshold
        self.use_gemini = use_gemini and GEMINI_AVAILABLE
        self.use_hash = use_hash and IMAGEHASH_AVAILABLE
        
        # Initialize Gemini client
        if self.use_gemini:
            api_key = api_key or os.environ.get("GEMINI_API_KEY")
            if not api_key:
                print("Warning: GEMINI_API_KEY not found. Gemini similarity detection disabled.")
                self.use_gemini = False
            else:
                try:
                    self.gemini_client = genai.Client(api_key=api_key)
                    print(f"Initialized Gemini client with model: {self.gemini_model}")
                except Exception as e:
                    print(f"Warning: Failed to initialize Gemini client: {e}")
                    self.use_gemini = False
        
        if not self.use_gemini and not self.use_hash:
            raise RuntimeError("No similarity detection methods available")
        
        print(f"Active methods: Gemini={self.use_gemini}, Hash={self.use_hash}")
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """Encode image to base64 for Gemini API"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding image {image_path}: {e}")
            return None
    
    def get_gemini_similarity(self, image1_path: str, image2_path: str) -> float:
        """Get similarity score between two images using Gemini API"""
        if not self.use_gemini:
            return 0.0
        
        try:
            # Encode both images
            img1_b64 = self.encode_image_to_base64(image1_path)
            img2_b64 = self.encode_image_to_base64(image2_path)
            
            if not img1_b64 or not img2_b64:
                return 0.0
            
            # Create prompt for similarity comparison
            prompt = """Compare these two images and determine their similarity on a scale of 0.0 to 1.0, where:
- 1.0 = Identical or nearly identical images
- 0.8-0.9 = Very similar (same subject, minor differences in angle, lighting, or editing)
- 0.6-0.7 = Similar (same subject, noticeable differences)
- 0.4-0.5 = Somewhat similar (related content or composition)
- 0.0-0.3 = Different images

Consider factors like:
- Subject matter and composition
- Colors and lighting
- Angles and perspective
- Overall visual similarity

Respond with only a single number between 0.0 and 1.0 representing the similarity score."""

            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=prompt),
                        types.Part.from_bytes(
                            data=base64.b64decode(img1_b64),
                            mime_type="image/jpeg"
                        ),
                        types.Part.from_bytes(
                            data=base64.b64decode(img2_b64),
                            mime_type="image/jpeg"
                        ),
                    ],
                ),
            ]
            
            generate_content_config = types.GenerateContentConfig(
                response_mime_type="text/plain",
                max_output_tokens=10,
                temperature=0.1,
            )
            
            response = self.gemini_client.models.generate_content(
                model=self.gemini_model,
                contents=contents,
                config=generate_content_config,
            )
            
            # Parse the similarity score
            try:
                similarity_score = float(response.text.strip())
                return max(0.0, min(1.0, similarity_score))  # Clamp to [0, 1]
            except ValueError:
                print(f"Warning: Could not parse Gemini response: {response.text}")
                return 0.0
                
        except Exception as e:
            print(f"Error getting Gemini similarity: {e}")
            return 0.0
    
    def get_hash_similarity(self, image1_path: str, image2_path: str) -> float:
        """Get similarity score using perceptual hashing"""
        if not self.use_hash:
            return 0.0
        
        try:
            img1 = Image.open(image1_path)
            img2 = Image.open(image2_path)
            
            hash1 = imagehash.phash(img1)
            hash2 = imagehash.phash(img2)
            
            hash_diff = hash1 - hash2
            # Convert hash difference to similarity score (0-1)
            max_diff = 64  # Maximum possible hash difference
            similarity = 1.0 - (hash_diff / max_diff)
            return max(0.0, similarity)
            
        except Exception as e:
            print(f"Error calculating hash similarity: {e}")
            return 0.0
    
    def are_images_similar(self, image1_path: str, image2_path: str) -> bool:
        """Determine if two images are similar using available methods"""
        similarities = []
        
        # Get Gemini similarity
        if self.use_gemini:
            gemini_sim = self.get_gemini_similarity(image1_path, image2_path)
            similarities.append(gemini_sim >= self.gemini_threshold)
            print(f"Gemini similarity: {gemini_sim:.3f} (threshold: {self.gemini_threshold})")
        
        # Get hash similarity
        if self.use_hash:
            hash_sim = self.get_hash_similarity(image1_path, image2_path)
            hash_similar = hash_sim >= (1.0 - self.hash_threshold / 64.0)
            similarities.append(hash_similar)
            print(f"Hash similarity: {hash_sim:.3f} (similar: {hash_similar})")
        
        # Return True if any method indicates similarity
        return any(similarities) if similarities else False

    def find_image_files(self, directory: str, extensions: List[str] = None) -> List[str]:
        """Find all image files in directory and subdirectories"""
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']

        image_files = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if any(file.lower().endswith(ext) for ext in extensions):
                    image_files.append(os.path.join(root, file))

        return sorted(image_files)

    def group_similar_images(self, image_files: List[str]) -> List[List[str]]:
        """Group similar images together"""
        print(f"Grouping {len(image_files)} images by similarity...")

        groups = []
        processed = set()

        for i, img1 in enumerate(image_files):
            if img1 in processed:
                continue

            current_group = [img1]
            processed.add(img1)

            print(f"Processing image {i+1}/{len(image_files)}: {os.path.basename(img1)}")

            # Compare with remaining images
            for j, img2 in enumerate(image_files[i+1:], i+1):
                if img2 in processed:
                    continue

                if self.are_images_similar(img1, img2):
                    current_group.append(img2)
                    processed.add(img2)
                    print(f"  -> Found similar: {os.path.basename(img2)}")

            groups.append(current_group)
            print(f"Group {len(groups)}: {len(current_group)} images")

        return groups

    def create_batches(self, groups: List[List[str]], batch_size: int = 4) -> List[List[str]]:
        """Create batches from similarity groups"""
        batches = []

        for group in groups:
            if len(group) == 1:
                # Single image - keep as is
                batches.append(group)
            else:
                # Multiple similar images - create batches
                for i in range(0, len(group), batch_size):
                    batch = group[i:i + batch_size]
                    batches.append(batch)

        return batches

    def process_directory(self,
                         input_dir: str,
                         output_dir: str,
                         batch_size: int = 4,
                         target_size: Optional[Tuple[int, int]] = None,
                         layout_mode: str = "grid",
                         spacing: int = 10,
                         background_color: str = "white",
                         quality: int = 95,
                         generate_master_file: bool = False,
                         extensions: List[str] = None) -> List[List[str]]:
        """
        Process a directory of images, finding variations and creating combined images

        Returns:
            List of batches (each batch is a list of image paths)
        """
        print(f"Processing directory: {input_dir}")
        print(f"Output directory: {output_dir}")
        print(f"Batch size: {batch_size}")
        print(f"Layout: {layout_mode}")

        # Find all image files
        image_files = self.find_image_files(input_dir, extensions)
        if not image_files:
            print("No image files found!")
            return []

        print(f"Found {len(image_files)} image files")

        # Group similar images
        groups = self.group_similar_images(image_files)
        print(f"Created {len(groups)} similarity groups")

        # Create batches
        batches = self.create_batches(groups, batch_size)
        print(f"Created {len(batches)} batches for processing")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Process each batch
        master_file_content = []

        for i, batch in enumerate(batches):
            batch_name = f"batch_{i+1:03d}"

            if len(batch) == 1:
                # Single image - copy as is
                src_path = batch[0]
                filename = os.path.basename(src_path)
                name, ext = os.path.splitext(filename)
                dst_path = os.path.join(output_dir, f"{batch_name}_{name}{ext}")

                shutil.copy2(src_path, dst_path)
                print(f"Copied single image: {filename} -> {os.path.basename(dst_path)}")

                # Add to master file
                if generate_master_file:
                    master_file_content.append(f"# {batch_name} (single image)")
                    master_file_content.append(os.path.basename(src_path))
                    master_file_content.append("")
            else:
                # Multiple images - combine them
                output_path = os.path.join(output_dir, f"{batch_name}_combined.jpg")

                try:
                    ImageCombiner.combine_images(
                        image_paths=batch,
                        output_path=output_path,
                        layout_mode=layout_mode,
                        target_size=target_size,
                        spacing=spacing,
                        background_color=background_color,
                        quality=quality
                    )
                    print(f"Combined {len(batch)} images -> {os.path.basename(output_path)}")

                    # Add to master file
                    if generate_master_file:
                        master_file_content.append(f"# {batch_name} ({len(batch)} images)")
                        for img_path in batch:
                            master_file_content.append(os.path.basename(img_path))
                        master_file_content.append("")

                except Exception as e:
                    print(f"Error combining batch {i+1}: {e}")

        # Generate master file if requested
        if generate_master_file and master_file_content:
            master_file_path = os.path.join(output_dir, "master_batch_list.txt")
            with open(master_file_path, 'w') as f:
                f.write('\n'.join(master_file_content))
            print(f"Generated master file: {master_file_path}")

        return batches
