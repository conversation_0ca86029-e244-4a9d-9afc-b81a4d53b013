#!/usr/bin/env python3
"""
Launcher for Gemini-Powered Image Variation Combiner GUI
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gemini_image_combiner_gui import main
    main()
except ImportError as e:
    print(f"Error importing GUI: {e}")
    print("Please make sure all dependencies are installed:")
    print("pip install google-genai pillow imagehash tkinter")
    sys.exit(1)
except Exception as e:
    print(f"Error running GUI: {e}")
    sys.exit(1)
